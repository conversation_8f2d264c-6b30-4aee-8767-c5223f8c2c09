#!/usr/bin/env python3
"""
测试子区域预处理优化
"""

try:
    # 测试导入和预处理
    from 52_conflict_calculation import SUBREGIONS_POLYGONS, _get_ship_subregion
    
    print("✅ 成功导入预处理的子区域数据")
    print(f"预处理的子区域数量: {len(SUBREGIONS_POLYGONS)}")
    print(f"子区域名称: {list(SUBREGIONS_POLYGONS.keys())}")
    
    # 测试子区域判断函数
    if SUBREGIONS_POLYGONS:
        # 使用一个测试坐标
        test_position = (121.3, 31.55)  # 示例坐标
        result = _get_ship_subregion(test_position, SUBREGIONS_POLYGONS)
        print(f"测试位置 {test_position} 属于子区域: {result}")
        
        print("✅ 子区域预处理优化测试通过")
    else:
        print("⚠️  没有找到有效的子区域数据")
        
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
