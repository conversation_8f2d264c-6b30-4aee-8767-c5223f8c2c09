import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.image as mpimg
from matplotlib.patches import Ellipse
from scipy.interpolate import interp1d, CubicSpline
import pickle
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


class ConflictFullSegmentVisualizer:
    """
    整段冲突可视化器
    """

    def __init__(self):
        """初始化可视化器"""
        self.base_map = None
        self.base_map_extent = [121.050, 121.350, 31.516, 31.784]
        self._load_base_map()

    def _load_base_map(self):
        """加载底图"""
        map_path = 'data/map0.png'
        if os.path.exists(map_path):
            try:
                self.base_map = mpimg.imread(map_path)
            except Exception as e:
                self.base_map = None

    def _draw_channel_boundaries(self, ax):
        """绘制航道边界线"""
        try:
            with open('data/geo_info.pkl', 'rb') as f:
                geo_info = pickle.load(f)

            # 绘制航道边界1
            if 'channel_side1' in geo_info:
                side1_data = geo_info['channel_side1']
                if side1_data and len(side1_data) > 0:
                    side1_lons = [p[0] for p in side1_data]
                    side1_lats = [p[1] for p in side1_data]
                    ax.plot(side1_lons, side1_lats, 'green', linewidth=3, alpha=0.8, linestyle='--')

            # 绘制航道边界2
            if 'channel_side2' in geo_info:
                side2_data = geo_info['channel_side2']
                if side2_data and len(side2_data) > 0:
                    side2_lons = [p[0] for p in side2_data]
                    side2_lats = [p[1] for p in side2_data]
                    ax.plot(side2_lons, side2_lats, 'green', linewidth=3, alpha=0.8, linestyle='--')

            # 绘制航道中心线
            if 'channel_centerline' in geo_info:
                centerline_data = geo_info['channel_centerline']
                if centerline_data and len(centerline_data) > 0:
                    center_lons = [p[0] for p in centerline_data]
                    center_lats = [p[1] for p in centerline_data]
                    ax.plot(center_lons, center_lats, 'green', linewidth=2, alpha=0.6, linestyle='--')

        except Exception as e:
            pass

    def _calculate_figure_size(self, display_extent, base_width=12):
        """计算图片尺寸"""
        lon_range = display_extent[1] - display_extent[0]
        lat_range = display_extent[3] - display_extent[2]

        center_lat = (display_extent[2] + display_extent[3]) / 2
        lat_km_per_degree = 111.0
        lon_km_per_degree = 111.0 * np.cos(np.radians(center_lat))

        actual_lon_distance = lon_range * lon_km_per_degree
        actual_lat_distance = lat_range * lat_km_per_degree
        geographic_aspect_ratio = actual_lat_distance / actual_lon_distance

        height = base_width * geographic_aspect_ratio
        height = max(4, min(height, 20))

        return (base_width, height)

    def _draw_ship_domain(self, ax, lon, lat, cog, color, scene_type, ship_length):
        """绘制船舶领域椭圆"""
        # 根据场景类型和船舶长度获取椭圆参数
        if scene_type == 'crossing':
            if ship_length <= 100:
                a_meters, b_meters = 271, 192
            else:
                a_meters, b_meters = 375, 210
        else:  # overtaking
            if ship_length <= 100:
                a_meters, b_meters = 180, 85
            else:
                a_meters, b_meters = 290, 120

        # 将米转换为度
        lat_rad = np.radians(lat)
        meters_per_degree_lon = 111320 * np.cos(lat_rad)
        meters_per_degree_lat = 111320

        a_degrees = a_meters / meters_per_degree_lon
        b_degrees = b_meters / meters_per_degree_lat

        # 航向转换
        angle = 90 - cog

        # 创建椭圆
        ellipse = Ellipse((lon, lat), width=2 * a_degrees, height=2 * b_degrees, angle=angle,
                          facecolor=color, alpha=0.3, edgecolor=color, linewidth=1)
        ax.add_patch(ellipse)

    def _get_display_extent(self, segment_data, expand_ratio=0.3):
        """计算显示范围"""
        man_data = segment_data['maneuvering_ship']['full_segment_data']
        other_data = segment_data['other_ship']['full_segment_data']

        all_lons = list(man_data['longitudes']) + list(other_data['longitudes'])
        all_lats = list(man_data['latitudes']) + list(other_data['latitudes'])

        lon_min, lon_max = min(all_lons), max(all_lons)
        lat_min, lat_max = min(all_lats), max(all_lats)

        lon_range = lon_max - lon_min
        lat_range = lat_max - lat_min

        lon_expand = lon_range * expand_ratio
        lat_expand = lat_range * expand_ratio

        display_extent = [
            lon_min - lon_expand, lon_max + lon_expand,
            lat_min - lat_expand, lat_max + lat_expand
        ]

        return display_extent

    def _setup_base_plot(self, ax, display_extent, show_channel=True):
        """设置基础图形"""
        # 设置底图
        if self.base_map is not None:
            ax.imshow(self.base_map, extent=self.base_map_extent, aspect='auto', alpha=0.7)

        # 设置显示范围
        ax.set_xlim(display_extent[0], display_extent[1])
        ax.set_ylim(display_extent[2], display_extent[3])

        # 绘制航道边界线
        if show_channel:
            self._draw_channel_boundaries(ax)

        ax.set_xlabel('经度')
        ax.set_ylabel('纬度')

    def _find_key_moments(self, segment_data):
        """找到关键时刻的索引"""
        conflicts = segment_data['maneuvering_ship']['full_segment_data']['conflicts']
        
        # 找到非零冲突的开始和结束索引
        nonzero_indices = [i for i, c in enumerate(conflicts) if c > 0.001]
        
        if not nonzero_indices:
            return None
        
        start_idx = 0  # 开始时刻
        conflict_start_idx = nonzero_indices[0]  # 冲突开始时刻
        max_idx = conflicts.index(max(conflicts))  # 最大冲突时刻
        conflict_end_idx = nonzero_indices[-1]  # 冲突结束时刻
        end_idx = len(conflicts) - 1  # 结束时刻
        
        return {
            'start': start_idx,
            'conflict_start': conflict_start_idx,
            'max_conflict': max_idx,
            'conflict_end': conflict_end_idx,
            'end': end_idx
        }

    def visualize_segment(self, segment_data, output_dir='vis/conflict_full_segments',
                         show_channel=True, plot_types=None, smooth_curve=False):
        """
        可视化单个冲突段

        Args:
            segment_data: 冲突段数据
            output_dir: 输出目录
            show_channel: 是否显示航道
            plot_types: 要生成的图类型列表，可选：
                       ['combined_moments', 'conflict_start', 'conflict_end', 'conflict_curve']
                       如果为None，则生成所有图
            smooth_curve: 是否对冲突变化曲线进行简单平滑
        """
        os.makedirs(output_dir, exist_ok=True)

        # 获取关键时刻
        key_moments = self._find_key_moments(segment_data)
        if key_moments is None:
            print(f"轨迹 {segment_data['trajectory_index']} 没有有效的冲突数据")
            return

        # 计算显示范围
        display_extent = self._get_display_extent(segment_data)
        figsize = self._calculate_figure_size(display_extent, base_width=12)

        traj_idx = segment_data['trajectory_index']

        # 如果未指定plot_types，则生成所有图
        if plot_types is None:
            plot_types = ['combined_moments', 'conflict_start', 'conflict_end', 'conflict_curve']

        generated_count = 0

        # 1. 生成组合图：开始时刻 + 最大冲突时刻 + 结束时刻（在同一张图中）
        if 'combined_moments' in plot_types:
            self._create_combined_moments_plot(segment_data, key_moments, display_extent, figsize, output_dir, show_channel)
            generated_count += 1

        # 2. 生成冲突开始时刻单独图（带前一分钟轨迹）
        if 'conflict_start' in plot_types:
            self._create_single_moment_with_trajectory_plot(segment_data, key_moments['conflict_start'],
                                           '冲突开始时刻', 'conflict_start', display_extent, figsize, output_dir, show_channel, trajectory_duration_minutes=1)
            generated_count += 1

        # 3. 生成冲突结束时刻单独图（带前一分钟轨迹）
        if 'conflict_end' in plot_types:
            self._create_single_moment_with_trajectory_plot(segment_data, key_moments['conflict_end'],
                                           '冲突结束时刻', 'conflict_end', display_extent, figsize, output_dir, show_channel, trajectory_duration_minutes=1)
            generated_count += 1

        # 4. 生成冲突变化折线图
        if 'conflict_curve' in plot_types:
            self._create_conflict_curve_plot(segment_data, key_moments, output_dir, smooth_curve)
            generated_count += 1

        print(f"✅ 轨迹 {traj_idx} 生成了 {generated_count} 张图")

    def _create_combined_moments_plot(self, segment_data, key_moments, display_extent, figsize, output_dir, show_channel):
        """创建组合时刻图：在同一张图中显示开始、最大冲突、结束时刻"""
        fig, ax = plt.subplots(1, 1, figsize=figsize)

        # 设置基础图形
        self._setup_base_plot(ax, display_extent, show_channel)

        # 提取数据
        man_data = segment_data['maneuvering_ship']['full_segment_data']
        other_data = segment_data['other_ship']['full_segment_data']
        man_length = segment_data['maneuvering_ship']['length']
        other_length = segment_data['other_ship']['length']
        scene_type = segment_data['scene_type']
        traj_idx = segment_data['trajectory_index']

        # 确保使用共有的时间段 - 验证两船数据长度一致
        man_times = man_data['times']
        other_times = other_data['times']

        # 确保开始和结束时刻在共有的时间范围内
        max_valid_idx = min(len(man_times), len(other_times),
                           len(man_data['longitudes']), len(man_data['latitudes']),
                           len(other_data['longitudes']), len(other_data['latitudes'])) - 1

        # 调整关键时刻索引，确保在有效范围内
        safe_start = min(key_moments['start'], max_valid_idx)
        safe_max_conflict = min(key_moments['max_conflict'], max_valid_idx)
        safe_end = min(key_moments['end'], max_valid_idx)

        # 定义要绘制的时刻
        moments_to_plot = [
            (safe_start, '开始时刻'),
            (safe_max_conflict, '最大冲突'),
            (safe_end, '结束时刻')
        ]

        # 绘制整个过程的轨迹线
        # 机动船轨迹线 - 红色虚线
        ax.plot(man_data['longitudes'][:max_valid_idx+1],
                man_data['latitudes'][:max_valid_idx+1],
                'r', linewidth=2, alpha=0.7, label='机动船轨迹')

        # 其他船轨迹线 - 蓝色虚线
        ax.plot(other_data['longitudes'][:max_valid_idx+1],
                other_data['latitudes'][:max_valid_idx+1],
                'b', linewidth=2, alpha=0.7, label='其他船轨迹')

        # 绘制每个时刻的船舶位置和领域
        for idx, title in moments_to_plot:
            # 机动船
            man_lon = man_data['longitudes'][idx]
            man_lat = man_data['latitudes'][idx]
            man_cog = man_data['courses'][idx]

            # 其他船
            other_lon = other_data['longitudes'][idx]
            other_lat = other_data['latitudes'][idx]
            other_cog = other_data['courses'][idx]

            # 绘制船舶位置 - 统一颜色：红色机动船，蓝色其他船
            ax.plot(man_lon, man_lat, 'ro', markersize=8)
            ax.plot(other_lon, other_lat, 'bo', markersize=8)

            # 绘制船舶领域椭圆 - 统一颜色：红色机动船，蓝色其他船
            self._draw_ship_domain(ax, man_lon, man_lat, man_cog, 'red', scene_type, man_length)
            self._draw_ship_domain(ax, other_lon, other_lat, other_cog, 'blue', scene_type, other_length)

        ax.set_title('开始时刻 + 最大冲突时刻 + 结束时刻 + 完整轨迹')

        # 添加图例
        ax.legend(loc='upper right')

        plt.tight_layout()
        output_file = os.path.join(output_dir, f"segment_{traj_idx}_combined_moments.png")
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()

    def _create_single_moment_plot(self, segment_data, moment_idx, title, filename_suffix, display_extent, figsize, output_dir, show_channel):
        """创建单个时刻图"""
        fig, ax = plt.subplots(1, 1, figsize=figsize)

        # 设置基础图形
        self._setup_base_plot(ax, display_extent, show_channel)

        # 提取数据
        man_data = segment_data['maneuvering_ship']['full_segment_data']
        other_data = segment_data['other_ship']['full_segment_data']
        man_length = segment_data['maneuvering_ship']['length']
        other_length = segment_data['other_ship']['length']
        scene_type = segment_data['scene_type']
        traj_idx = segment_data['trajectory_index']

        # 绘制船舶位置和领域
        man_lon = man_data['longitudes'][moment_idx]
        man_lat = man_data['latitudes'][moment_idx]
        man_cog = man_data['courses'][moment_idx]

        other_lon = other_data['longitudes'][moment_idx]
        other_lat = other_data['latitudes'][moment_idx]
        other_cog = other_data['courses'][moment_idx]

        # 绘制船舶位置
        ax.plot(man_lon, man_lat, 'ro', markersize=8)
        ax.plot(other_lon, other_lat, 'bo', markersize=8)

        # 绘制船舶领域椭圆
        self._draw_ship_domain(ax, man_lon, man_lat, man_cog, 'red', scene_type, man_length)
        self._draw_ship_domain(ax, other_lon, other_lat, other_cog, 'blue', scene_type, other_length)

        ax.set_title(title)

        plt.tight_layout()
        output_file = os.path.join(output_dir, f"segment_{traj_idx}_{filename_suffix}.png")
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()

    def _create_single_moment_with_trajectory_plot(self, segment_data, moment_idx, title, filename_suffix, display_extent, figsize, output_dir, show_channel, trajectory_duration_minutes=1):
        """创建带前一段时间轨迹的单个时刻图"""
        fig, ax = plt.subplots(1, 1, figsize=figsize)

        # 设置基础图形
        self._setup_base_plot(ax, display_extent, show_channel)

        # 提取数据
        man_data = segment_data['maneuvering_ship']['full_segment_data']
        other_data = segment_data['other_ship']['full_segment_data']
        man_length = segment_data['maneuvering_ship']['length']
        other_length = segment_data['other_ship']['length']
        scene_type = segment_data['scene_type']
        traj_idx = segment_data['trajectory_index']

        # 计算轨迹起始索引（前一分钟）
        # 假设数据采样间隔为10秒，1分钟=6个数据点
        trajectory_points = int(trajectory_duration_minutes * 60 / 10)  # 60秒/10秒间隔 = 6个点
        start_idx = max(0, moment_idx - trajectory_points)

        # 确保索引在有效范围内
        max_valid_idx = min(len(man_data['times']), len(other_data['times']),
                           len(man_data['longitudes']), len(man_data['latitudes']),
                           len(other_data['longitudes']), len(other_data['latitudes'])) - 1

        safe_moment_idx = min(moment_idx, max_valid_idx)
        safe_start_idx = min(start_idx, max_valid_idx)

        # 绘制轨迹线（从起始时刻到当前时刻）
        if safe_start_idx < safe_moment_idx:
            # 机动船轨迹线 - 红色虚线
            ax.plot(man_data['longitudes'][safe_start_idx:safe_moment_idx+1],
                    man_data['latitudes'][safe_start_idx:safe_moment_idx+1],
                    'r', linewidth=4, alpha=0.7, label='机动船轨迹')

            # 其他船轨迹线 - 蓝色虚线
            ax.plot(other_data['longitudes'][safe_start_idx:safe_moment_idx+1],
                    other_data['latitudes'][safe_start_idx:safe_moment_idx+1],
                    'b', linewidth=4, alpha=0.7, label='其他船轨迹')

        # 绘制当前时刻的船舶位置和领域
        man_lon = man_data['longitudes'][safe_moment_idx]
        man_lat = man_data['latitudes'][safe_moment_idx]
        man_cog = man_data['courses'][safe_moment_idx]

        other_lon = other_data['longitudes'][safe_moment_idx]
        other_lat = other_data['latitudes'][safe_moment_idx]
        other_cog = other_data['courses'][safe_moment_idx]

        # 绘制船舶位置
        ax.plot(man_lon, man_lat, 'ro', markersize=8)
        ax.plot(other_lon, other_lat, 'bo', markersize=8)

        # 绘制船舶领域椭圆
        self._draw_ship_domain(ax, man_lon, man_lat, man_cog, 'red', scene_type, man_length)
        self._draw_ship_domain(ax, other_lon, other_lat, other_cog, 'blue', scene_type, other_length)

        ax.set_title(f'{title} + 前{trajectory_duration_minutes}分钟轨迹')

        # 添加图例（如果有轨迹线）
        if safe_start_idx < safe_moment_idx:
            ax.legend(loc='upper right')

        plt.tight_layout()
        output_file = os.path.join(output_dir, f"segment_{traj_idx}_{filename_suffix}.png")
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()

    def _create_conflict_curve_plot(self, segment_data, key_moments, output_dir, smooth=False):
        """创建冲突变化折线图"""
        fig, ax = plt.subplots(1, 1, figsize=(10, 6))

        man_data = segment_data['maneuvering_ship']['full_segment_data']
        conflicts = man_data['conflicts']
        traj_idx = segment_data['trajectory_index']

        # 原始数据：每20秒一个点
        x_original = np.array([i * 20 for i in range(len(conflicts))])
        y_original = np.array(conflicts)

        # 如果需要平滑处理（三次样条插值到1秒间隔）
        if smooth and len(conflicts) > 2:  # 至少需要3个点才能做三次样条
            x_data, y_data = self._smooth_interpolate_to_1s(x_original, y_original)
        else:
            x_data, y_data = x_original, y_original

        # 绘制冲突变化曲线 - 使用蓝色折线
        ax.plot(x_data, y_data, 'b-', linewidth=2)

        # 设置x轴刻度：每60秒显示一个刻度，避免过于密集
        max_time = x_original[-1]
        x_ticks = np.arange(0, max_time + 1, 60)  # 每60秒一个刻度
        ax.set_xticks(x_ticks)

        # 设置刻度朝内
        ax.tick_params(axis='both', direction='in')

        ax.set_xlabel('时间 (秒)')
        ax.set_ylabel('冲突值')
        ax.set_title('冲突变化过程')

        plt.tight_layout()
        output_file = os.path.join(output_dir, f"segment_{traj_idx}_conflict_curve.png")
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()

    def _smooth_interpolate_to_1s(self, x_original, y_original):
        """使用三次样条插值将20秒间隔插值成1秒间隔，保证边界数据正确"""
        try:
            # 使用三次样条插值，设置边界条件为自然边界（二阶导数为0）
            cs = CubicSpline(x_original, y_original, bc_type='natural')

            # 创建1秒间隔的x轴数据
            x_interp = np.arange(x_original[0], x_original[-1] + 1, 1)

            # 进行三次样条插值
            y_interp = cs(x_interp)

            # 确保边界点完全正确（强制设置原始数据点的值）
            for i, x_orig in enumerate(x_original):
                if x_orig in x_interp:
                    idx = np.where(x_interp == x_orig)[0][0]
                    y_interp[idx] = y_original[i]

            return x_interp, y_interp

        except Exception as e:
            # 如果三次样条插值失败，回退到线性插值
            print(f"三次样条插值失败，使用线性插值: {e}")
            try:
                f = interp1d(x_original, y_original, kind='linear')
                x_interp = np.arange(x_original[0], x_original[-1] + 1, 1)
                y_interp = f(x_interp)
                return x_interp, y_interp
            except Exception as e2:
                print(f"线性插值也失败，使用原始数据: {e2}")
                return x_original, y_original



    def batch_visualize(self, segments_data, scene_type, output_dir='vis/conflict_full_segments',
                       show_channel=True, plot_types=None, smooth_curve=False):
        """批量可视化"""
        if not segments_data:
            print(f"无{scene_type}冲突段数据")
            return

        scene_output_dir = os.path.join(output_dir, scene_type)
        print(f"开始生成{scene_type}场景冲突段图...")

        for i, segment in enumerate(segments_data):
            try:
                self.visualize_segment(segment, output_dir=scene_output_dir,
                                     show_channel=show_channel, plot_types=plot_types,
                                     smooth_curve=smooth_curve)
            except Exception as e:
                print(f"❌ 生成第{i + 1}个失败: {e}")

        print(f"✅ {scene_type}场景完成，共{len(segments_data)}个")

    def visualize_custom(self, scene_type, trajectory_indices=None, show_channel=True,
                        plot_types=None, smooth_curve=False, output_dir='vis/custom_conflict_segments'):
        """
        自定义可视化指定场景类型和轨迹索引

        Args:
            scene_type: 场景类型 ('crossing' 或 'overtaking')
            trajectory_indices: 要可视化的轨迹索引列表，如果为None则可视化所有
            show_channel: 是否显示航道边界线
            plot_types: 要生成的图类型列表，可选：
                       ['combined_moments', 'conflict_start', 'conflict_end', 'conflict_curve']
            smooth_curve: 是否对冲突变化曲线进行简单平滑
            output_dir: 输出目录
        """
        # 加载对应场景的数据
        if scene_type == 'crossing':
            data_file = 'result/trajectories/2024_1/crossing_valid_full_segments.pkl'
        elif scene_type == 'overtaking':
            data_file = 'result/trajectories/2024_1/overtaking_valid_full_segments.pkl'
        else:
            print(f"❌ 不支持的场景类型: {scene_type}")
            return

        try:
            with open(data_file, 'rb') as f:
                segments_data = pickle.load(f)
        except FileNotFoundError:
            print(f"❌ 数据文件不存在: {data_file}")
            return

        if not segments_data:
            print(f"❌ {scene_type}场景无数据")
            return

        # 筛选指定的轨迹索引
        if trajectory_indices is not None:
            # 创建索引到数据的映射
            index_to_data = {seg['trajectory_index']: seg for seg in segments_data}

            # 筛选指定索引的数据
            selected_segments = []
            for idx in trajectory_indices:
                if idx in index_to_data:
                    selected_segments.append(index_to_data[idx])
                else:
                    print(f"⚠️ 轨迹索引 {idx} 不存在于{scene_type}场景数据中")

            if not selected_segments:
                print(f"❌ 没有找到指定的轨迹索引")
                return

            segments_data = selected_segments

        # 创建输出目录
        scene_output_dir = os.path.join(output_dir, scene_type)
        os.makedirs(scene_output_dir, exist_ok=True)

        print(f"开始自定义可视化{scene_type}场景...")
        print(f"轨迹数量: {len(segments_data)}")
        print(f"图类型: {plot_types if plot_types else '全部'}")
        print(f"显示航道: {'是' if show_channel else '否'}")
        print(f"曲线平滑: {'是' if smooth_curve else '否'}")

        # 可视化每个段
        for i, segment in enumerate(segments_data):
            try:
                self.visualize_segment(segment, output_dir=scene_output_dir,
                                     show_channel=show_channel, plot_types=plot_types,
                                     smooth_curve=smooth_curve)
            except Exception as e:
                print(f"❌ 生成轨迹 {segment['trajectory_index']} 失败: {e}")
                continue

        print(f"✅ 自定义可视化完成")

    def list_available_trajectories(self, scene_type):
        """列出指定场景类型的可用轨迹索引"""
        if scene_type == 'crossing':
            data_file = 'result/trajectories/2024_1/crossing_valid_full_segments.pkl'
        elif scene_type == 'overtaking':
            data_file = 'result/trajectories/2024_1/overtaking_valid_full_segments.pkl'
        else:
            print(f"❌ 不支持的场景类型: {scene_type}")
            return []

        try:
            with open(data_file, 'rb') as f:
                segments_data = pickle.load(f)
        except FileNotFoundError:
            print(f"❌ 数据文件不存在: {data_file}")
            return []

        indices = [seg['trajectory_index'] for seg in segments_data]
        print(f"{scene_type}场景可用轨迹索引: {sorted(indices)}")
        return sorted(indices)


def main():
    """主函数 - 在这里直接指定可视化参数"""
    visualizer = ConflictFullSegmentVisualizer()

    # ==================== 在这里修改参数 ====================

    # 选择功能模式：
    # 'batch' - 批量生成所有场景
    # 'custom' - 自定义可视化
    # 'list' - 查看可用轨迹索引
    mode = 'custom'

    # 自定义可视化参数（仅在mode='custom'时有效）
    scene_type = 'crossing'  # 'crossing' 或 'overtaking'
    trajectory_indices = [187]  # 指定轨迹索引，None表示全部
    # trajectory_indices = [8, 15, 24]  # 指定轨迹索引，None表示全部

    scene_type = 'overtaking'  # 'crossing' 或 'overtaking'
    trajectory_indices = [1807]  # 指定轨迹索引，None表示全部
    # trajectory_indices = [15, 22, 72]  # 指定轨迹索引，None表示全部

    
    show_channel = True  # 是否显示航道边界线
    smooth_curve = True  # 是否对冲突变化曲线进行简单平滑
    
    plot_types = ['combined_moments',  'conflict_start','conflict_end','conflict_curve']  # 指定图类型，None表示全部
    # plot_types = ['conflict_curve']  # 指定图类型，None表示全部
    
    # 可选图类型：
    # 'combined_moments' - 组合时刻图（开始+最大冲突+结束）
    # 'conflict_start' - 冲突开始时刻图
    # 'conflict_end' - 冲突结束时刻图
    # 'conflict_curve' - 冲突变化折线图

    # 查看索引参数（仅在mode='list'时有效）
    list_scene_type = 'crossing'  # 'crossing' 或 'overtaking'

    # ========================================================

    if mode == 'batch':
        # 批量生成所有场景
        print("批量生成所有场景...")
        try:
            with open('result/trajectories/2024_1/crossing_valid_full_segments.pkl', 'rb') as f:
                crossing_full_segments = pickle.load(f)
            with open('result/trajectories/2024_1/overtaking_valid_full_segments.pkl', 'rb') as f:
                overtaking_full_segments = pickle.load(f)

            print(f"交叉场景: {len(crossing_full_segments)} 个")
            print(f"追越场景: {len(overtaking_full_segments)} 个")

            # 生成所有图
            visualizer.batch_visualize(crossing_full_segments, "crossing", show_channel=True)
            visualizer.batch_visualize(overtaking_full_segments, "overtaking", show_channel=True)

            print("\n🎬 所有冲突段可视化完成！")
        except FileNotFoundError as e:
            print(f"❌ 数据文件不存在: {e}")

    elif mode == 'custom':
        # 自定义可视化
        print(f"自定义可视化 - 场景: {scene_type}")
        print(f"轨迹索引: {trajectory_indices if trajectory_indices else '全部'}")
        print(f"图类型: {plot_types if plot_types else '全部'}")
        print(f"显示航道: {'是' if show_channel else '否'}")
        print(f"曲线平滑: {'是' if smooth_curve else '否'}")

        visualizer.visualize_custom(scene_type, trajectory_indices, show_channel, plot_types, smooth_curve)

    elif mode == 'list':
        # 查看可用轨迹索引
        print(f"查看{list_scene_type}场景可用轨迹索引:")
        visualizer.list_available_trajectories(list_scene_type)

    else:
        print("❌ 无效的模式，请设置mode为 'batch', 'custom' 或 'list'")


def quick_visualize(scene_type, trajectory_indices=None, show_channel=True, plot_types=None, smooth_curve=False):
    """
    快速可视化函数 - 用于脚本调用

    Args:
        scene_type: 'crossing' 或 'overtaking'
        trajectory_indices: 轨迹索引列表，如 [3, 5, 7]
        show_channel: 是否显示航道边界线
        plot_types: 图类型列表，如 ['combined_moments', 'conflict_curve']
        smooth_curve: 是否对冲突变化曲线进行简单平滑

    Example:
        # 可视化交叉场景的轨迹3和5，只显示组合时刻图和平滑的冲突曲线，不显示航道
        quick_visualize('crossing', [3, 5], show_channel=False,
                       plot_types=['combined_moments', 'conflict_curve'], smooth_curve=True)
    """
    visualizer = ConflictFullSegmentVisualizer()
    visualizer.visualize_custom(scene_type, trajectory_indices, show_channel, plot_types, smooth_curve)


if __name__ == '__main__':
    main()
